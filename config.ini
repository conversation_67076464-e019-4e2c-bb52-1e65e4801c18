[txt2sql.azure_openai]
api_version = 2024-02-01
api_embedding_endpoint = https://epr002-oai-bots-dev-sdc-001.openai.azure.com/
api_llm_endpoint = 	https://epr002-oai-bots-dev-sdc-001.openai.azure.com/
inquiry_embedder = embeddings
llm_deployed = gpt-4o-mini

[txt2sql.preliminary]
model_deployed = gpt-4o-mini

[translator.azure_ai_api]
api_version = 2025-01-01-preview
api_llm_endpoint = 	https://epr002-oai-cmn-stg-weu-001.openai.azure.com/
api_translator_endpoint = https://api.cognitive.microsofttranslator.com
llm_deployed = gpt-4.1-mini

[translator_bot.azure_openai]
api_version = 2025-01-01-preview
api_llm_endpoint = https://epr-openai-sandbox-plus.openai.azure.com/
llm_deployed = gpt-4.1-mini

[compli_bot.txt2sql.azure_ai_search]
search_index = txt2sql-examples
search_endpoint = https://epr002-srch-bots-dev-sdc-001.search.windows.net/

[call_center_bot.txt2sql.azure_ai_search]
search_index = txt2sql-examples
search_endpoint = https://epr002-srch-bots-dev-sdc-001.search.windows.net/

[web]
flask_app = src
flask_debug = 1

[txt2sql.oracle_oci]
oci_client_path = c:\
oci_dsn = dwhtest_high
oci_schema = DWH_PUBLIC

[common]
run_mode = TEST
version_gitrev = 97f862
version_gittag = nn
version_release = 1.0.3
logout_url = https://electroluxprofessional.unily.com

[log]
loglevel = INFO
log_folder = .

[ad]
client_id = f583e81c-71f6-47b3-860b-f7503f95ba56
authority_uri = 7849ddb5-cc3f-42e6-b0f1-1102b2c2600c
redirect_uri = https://localhost:5000/cb/getAToken
ad_scope=.default
ad_endpoint_api=https://graph.microsoft.com/v1.0/me
ad_schema_callback=https

[db]
db_name =  complibot
db_host = eldbt0909.epr.electroluxprofessional.com
db_port = 1433
db_user = complibot_user
db_driver = ODBC Driver 17 for SQL Server

[azure_openai]
api_version = 2025-01-01-preview
api_llm_endpoint = https://epr-openai-sandbox-plus.openai.azure.com/
llm_deployed = gpt-4.1-mini

[call_center_bot.rag.storage]
endpoint = https://epr002-srch-bots-all-weu-001.search.windows.net
index_name = it-documents-pride

[how_to_bot.rag.storage]
endpoint = https://epr-ai-srch-rag.search.windows.net
index_name = how-to-bot-documents

[rag.parameters]
vector_search_profile = hnsw
vector_algorithm = hnsw-cosine
semantic_configuration = semantic-rerank
embedding_resource = https://epr-openai-sandbox-plus.openai.azure.com/
embedding_model = embedding-test-rag
embedding_api_version = 2023-12-01-preview

[rag.loader]
document_intelligence_endpoint = https://epr-ai-rag-di-weu-dev-loader.cognitiveservices.azure.com/

[rag.generator]
endpoint = https://epr002-oai-bots-dev-sdc-001.openai.azure.com/
model = gpt-4o-mini
temperature = 0.25
version = 2023-12-01-preview

[call_center_bot.rag.fetcher]
local_root =
pride_products_root =

[how_to_bot.rag.fetcher]
local_root =
pride_products_root =

[call_center_bot.azure_blob_storage]
account_url = https://epraisandboxsa.blob.core.windows.net/
account_name = epraisandboxsa
table_container = pride-tables-rag-dev
image_container = pride-images-rag-dev

[how_to_bot.azure_blob_storage]
account_url = https://epraisandboxsa.blob.core.windows.net/
account_name = epraisandboxsa
table_container = how-to-bot-tables-rag
image_container = how-to-bot-images-rag


[jdanallo.azure_blob_storage]
account_url = https://epraisandboxsa.blob.core.windows.net/
account_name = epraisandboxsa
table_container = jdallo-tables-rag-dev
image_container = jdallo-images-rag-dev

[jdanallo.rag.storage]
endpoint = https://epr-ai-srch-rag.search.windows.net
index_name = jde-documents

[jdanallo.rag.fetcher]
local_root = C:\Users\<USER>\JDE_Documentation
pride_products_root =

[jdanallo.rag.param]
semantic_configuration = semantic-jde
vector_search_profile = vector-profile-jde


[seobot.rag.fetcher]
local_root = C:\Users\<USER>\SEO_Documentation
pride_products_root =

[seobot.rag.storage]
endpoint = https://epr-ai-srch-rag.search.windows.net
index_name = seo-bot-documents

[seobot.rag.param]
semantic_configuration = semantic-seo
vector_search_profile = vector-profile-seo

[seobot.azure_blob_storage]
account_url = https://epraisandboxsa.blob.core.windows.net/
account_name = epraisandboxsa
table_container = seo-bot-tables-rag
image_container = seo-bot-images-rag